#!/usr/bin/env python3
import fitz  # PyMuPDF
from paddleocr import PaddleOCR
import os
from concurrent.futures import ThreadPoolExecutor, as_completed

# 初始化 PaddleOCR，使用更简单的配置
ocr = PaddleOCR(use_angle_cls=True, lang="ch", show_log=False)  # 使用基础配置

# 设置输入和输出路径
input_pdf = "PDFtest.pdf"  # 单个PDF文件或目录路径
output_pdf_dir = "output_pdfs"  # 输出 OCR 后的 PDF 文件夹
os.makedirs(output_pdf_dir, exist_ok=True)

# 每页 OCR 处理函数（文件方式）
def ocr_page(task):
    idx, img_bytes = task
    temp_img_path = f"temp_page_{idx}_{os.getpid()}.png"

    try:
        # 保存临时图像文件
        with open(temp_img_path, 'wb') as f:
            f.write(img_bytes)

        print(f"开始OCR处理第 {idx+1} 页...")

        # 使用 PaddleOCR 进行识别
        try:
            result = ocr.ocr(temp_img_path)
        except Exception as ocr_error:
            print(f"OCR处理第 {idx+1} 页出错: {str(ocr_error)}")
            return idx, f"OCR处理出错: {str(ocr_error)}"

        # 提取文本行
        lines = []
        if result and result[0]:
            for line in result[0]:
                if len(line) >= 2 and len(line[1]) >= 1:
                    text_content = line[1][0]
                    confidence = line[1][1] if len(line[1]) >= 2 else 0
                    # 只保留置信度较高的文本
                    if confidence > 0.5:
                        lines.append(text_content)

        text = "\n".join(lines) if lines else "未识别到文字"
        print(f"第 {idx+1} 页OCR完成，识别到 {len(lines)} 行文字")

    except Exception as e:
        print(f"处理第 {idx+1} 页时出错: {str(e)}")
        text = f"处理出错: {str(e)}"
    finally:
        # 清理临时文件
        if os.path.exists(temp_img_path):
            try:
                os.remove(temp_img_path)
            except:
                pass

    return idx, text

# 处理单个 PDF 并输出为 OCR PDF
def process_pdf(pdf_path):
    print(f"开始处理PDF: {pdf_path}")
    pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
    output_pdf_path = os.path.join(output_pdf_dir, f"{pdf_name}_ocr.pdf")
    print(f"输出文件路径: {output_pdf_path}")

    try:
        src_doc = fitz.open(pdf_path)
        print(f"PDF打开成功，共 {src_doc.page_count} 页")
        matrix = fitz.Matrix(300/72, 300/72)
        tasks = []
        # 提取所有页面图像到内存
        for i in range(src_doc.page_count):
            print(f"提取第 {i+1} 页图像...")
            page = src_doc.load_page(i)
            pix = page.get_pixmap(matrix=matrix)
            # 获取 PNG 格式的字节数据，替换 getPNGData
            img_bytes = pix.tobytes("png")
            tasks.append((i, img_bytes))
            print(f"第 {i+1} 页图像提取完成，大小: {len(img_bytes)} 字节")
    except Exception as e:
        print(f"PDF处理出错: {str(e)}")
        return

    # 并行 OCR
    print(f"开始OCR处理，使用 {min(4, os.cpu_count() or 1)} 个线程...")
    results = {}
    max_workers = min(4, os.cpu_count() or 1)
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(ocr_page, t) for t in tasks]
        completed_count = 0
        for f in as_completed(futures):
            idx, text = f.result()
            results[idx] = text
            completed_count += 1
            print(f"OCR完成进度: {completed_count}/{len(tasks)}")

    print("OCR处理完成，开始生成PDF...")
    # 生成带文本的 PDF
    try:
        dst_doc = fitz.open()
        for i in range(src_doc.page_count):
            text = results.get(i, "未识别到文字")
            print(f"添加第 {i+1} 页到输出PDF，文本长度: {len(text)}")
            dst_page = dst_doc.new_page(width=595, height=842)
            text_rect = fitz.Rect(40, 40, 555, 802)
            dst_page.insert_textbox(
                text_rect,
                text,
                fontsize=12,
                fontname="helv",
                align=0
            )
        dst_doc.save(output_pdf_path)
        print(f"OCR 输出 PDF 完成: {output_pdf_path}")

        src_doc.close()
        dst_doc.close()
    except Exception as e:
        print(f"生成PDF时出错: {str(e)}")
        import traceback
        traceback.print_exc()

# 主入口：支持单文件或目录批量处理
if __name__ == "__main__":
    print(f"开始处理，输入路径: {input_pdf}")
    print(f"输出目录: {output_pdf_dir}")

    if os.path.exists(input_pdf):
        print(f"找到输入文件/目录: {input_pdf}")
        if os.path.isfile(input_pdf) and input_pdf.lower().endswith('.pdf'):
            print(f"开始处理单个PDF文件: {input_pdf}")
            try:
                process_pdf(input_pdf)
            except Exception as e:
                print(f"处理PDF时出错: {str(e)}")
                import traceback
                traceback.print_exc()
        elif os.path.isdir(input_pdf):
            print(f"处理目录: {input_pdf}")
            pdf_files = [f for f in os.listdir(input_pdf) if f.lower().endswith('.pdf')]
            if pdf_files:
                print(f"找到 {len(pdf_files)} 个PDF文件")
                for f in pdf_files:
                    try:
                        process_pdf(os.path.join(input_pdf, f))
                    except Exception as e:
                        print(f"处理 {f} 时出错: {str(e)}")
            else:
                print(f"目录 {input_pdf} 中未找到 PDF 文件")
        else:
            print(f"错误: {input_pdf} 不是有效的 PDF 文件或目录")
    else:
        print(f"错误: 未找到文件或目录 {input_pdf}")

    print("程序执行完成")
