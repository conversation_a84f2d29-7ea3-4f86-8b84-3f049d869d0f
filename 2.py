#!/usr/bin/env python3
import fitz  # PyMuPDF
from paddleocr import PaddleOCR
import os, io
from PIL import Image
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed

# 初始化 PaddleOCR，设置为中文
ocr = PaddleOCR(use_textline_orientation=True, lang="ch")  # 已替换弃用参数 use_angle_cls

# 设置输入和输出路径
input_pdf = "PDFtest.pdf"  # 单个PDF文件或目录路径
output_pdf_dir = "output_pdfs"  # 输出 OCR 后的 PDF 文件夹
os.makedirs(output_pdf_dir, exist_ok=True)

# 每页 OCR 处理函数（内存方式）
def ocr_page(task):
    idx, img_bytes = task
    # 从内存字节加载图像
    img = Image.open(io.BytesIO(img_bytes))
    img_np = np.array(img)
    try:
        # 使用 PaddleOCR 进行识别
        try:
            result = ocr.predict(img_np)
        except AttributeError:
            result = ocr.ocr(img_np)
        # 提取文本行
        lines = []
        if result and result[0]:
            for line in result[0]:
                if len(line) >= 2 and len(line[1]) >= 1:
                    lines.append(line[1][0])
        text = "\n".join(lines) if lines else "未识别到文字"
    except Exception as e:
        text = f"OCR 出错: {e}"
    return idx, text

# 处理单个 PDF 并输出为 OCR PDF
def process_pdf(pdf_path):
    pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
    output_pdf_path = os.path.join(output_pdf_dir, f"{pdf_name}_ocr.pdf")
    
    src_doc = fitz.open(pdf_path)
    matrix = fitz.Matrix(300/72, 300/72)
    tasks = []
    # 提取所有页面图像到内存
    for i in range(src_doc.page_count):
        page = src_doc.load_page(i)
        pix = page.get_pixmap(matrix=matrix)
        # 获取 PNG 格式的字节数据，替换 getPNGData
        img_bytes = pix.tobytes("png")
        tasks.append((i, img_bytes))

    # 并行 OCR
    results = {}
    max_workers = min(4, os.cpu_count() or 1)
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(ocr_page, t) for t in tasks]
        for f in as_completed(futures):
            idx, text = f.result()
            results[idx] = text

    # 生成带文本的 PDF
    dst_doc = fitz.open()
    for i in range(src_doc.page_count):
        text = results.get(i, "未识别到文字")
        dst_page = dst_doc.new_page(width=595, height=842)
        text_rect = fitz.Rect(40, 40, 555, 802)
        dst_page.insert_textbox(
            text_rect,
            text,
            fontsize=12,
            fontname="helv",
            align=0
        )
    dst_doc.save(output_pdf_path)
    print(f"OCR 输出 PDF 完成: {output_pdf_path}")

    src_doc.close()
    dst_doc.close()

# 主入口：支持单文件或目录批量处理
if __name__ == "__main__":
    if os.path.exists(input_pdf):
        if os.path.isfile(input_pdf) and input_pdf.lower().endswith('.pdf'):
            process_pdf(input_pdf)
        elif os.path.isdir(input_pdf):
            pdf_files = [f for f in os.listdir(input_pdf) if f.lower().endswith('.pdf')]
            if pdf_files:
                for f in pdf_files:
                    process_pdf(os.path.join(input_pdf, f))
            else:
                print(f"目录 {input_pdf} 中未找到 PDF 文件")
        else:
            print(f"错误: {input_pdf} 不是有效的 PDF 文件或目录")
    else:
        print(f"错误: 未找到文件或目录 {input_pdf}")
