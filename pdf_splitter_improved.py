import fitz  # PyMuPDF
import os

def split_double_layer_pdf_improved(input_pdf_path, output_text_pdf=None, output_image_pdf=None):
    """
    改进版PDF分离器 - 将双层PDF分离成文本PDF和图像PDF
    """
    
    if not os.path.exists(input_pdf_path):
        print(f"错误: 找不到文件 {input_pdf_path}")
        return False
    
    # 设置默认输出文件名
    base_name = os.path.splitext(input_pdf_path)[0]
    if output_text_pdf is None:
        output_text_pdf = f"{base_name}_text_improved.pdf"
    if output_image_pdf is None:
        output_image_pdf = f"{base_name}_images_improved.pdf"
    
    try:
        # 打开原始PDF
        pdf_doc = fitz.open(input_pdf_path)
        
        # 创建文本PDF
        create_text_pdf_improved(pdf_doc, output_text_pdf)
        
        # 创建图像PDF
        create_image_pdf_improved(pdf_doc, output_image_pdf)
        
        pdf_doc.close()
        
        print(f"✅ 改进版分离完成!")
        print(f"📄 文本PDF: {output_text_pdf}")
        print(f"🖼️  图像PDF: {output_image_pdf}")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理PDF时出错: {str(e)}")
        return False

def create_text_pdf_improved(pdf_doc, output_path):
    """创建纯文本PDF - 改进版"""
    print("正在创建改进版文本PDF...")
    
    # 创建新的PDF文档
    text_doc = fitz.open()
    
    for page_num in range(pdf_doc.page_count):
        page = pdf_doc.load_page(page_num)
        
        # 提取文本内容
        text_content = page.get_text()
        
        # 获取页面尺寸
        rect = page.rect
        
        # 创建新页面
        new_page = text_doc.new_page(width=rect.width, height=rect.height)
        
        if text_content.strip():  # 如果页面有文本内容
            try:
                # 使用简单的文本插入方法
                lines = text_content.split('\n')
                y_position = rect.height - 50  # 从顶部开始
                
                for line in lines:
                    if line.strip():  # 跳过空行
                        new_page.insert_text(
                            fitz.Point(50, y_position), 
                            line.strip(), 
                            fontsize=12,
                            fontname="helv"
                        )
                        y_position -= 15  # 行间距
                        
                        # 如果超出页面，停止添加
                        if y_position < 50:
                            break
                            
            except Exception as e:
                print(f"处理第{page_num + 1}页文本时出错: {e}")
                # 添加错误信息到页面
                new_page.insert_text(
                    fitz.Point(50, rect.height - 50), 
                    f"第{page_num + 1}页文本处理出错", 
                    fontsize=12
                )
        else:
            # 如果没有文本，创建标记页面
            new_page.insert_text(
                fitz.Point(50, rect.height - 50), 
                f"第{page_num + 1}页 - 无文本内容", 
                fontsize=12
            )
    
    # 保存文本PDF
    text_doc.save(output_path)
    text_doc.close()

def create_image_pdf_improved(pdf_doc, output_path):
    """创建纯图像PDF - 改进版"""
    print("正在创建改进版图像PDF...")
    
    # 创建新的PDF文档
    image_doc = fitz.open()
    
    for page_num in range(pdf_doc.page_count):
        page = pdf_doc.load_page(page_num)
        
        # 获取页面尺寸
        rect = page.rect
        
        # 方法1: 直接渲染整个页面为图像，然后移除文本
        try:
            # 将页面渲染为高分辨率图像
            mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放以提高质量
            pix = page.get_pixmap(matrix=mat, alpha=False)
            
            # 创建新页面
            new_page = image_doc.new_page(width=rect.width, height=rect.height)
            
            # 将渲染的图像插入新页面
            img_data = pix.tobytes("png")
            new_page.insert_image(rect, stream=img_data)
            
            # 在图像上覆盖白色矩形来遮盖文本区域
            # 这是一个简化的方法，实际效果可能需要调整
            text_blocks = page.get_text("dict")["blocks"]
            for block in text_blocks:
                if "lines" in block:  # 文本块
                    block_rect = fitz.Rect(block["bbox"])
                    # 用白色矩形覆盖文本区域
                    new_page.draw_rect(block_rect, color=(1, 1, 1), fill=(1, 1, 1))
            
            pix = None  # 释放内存
            
        except Exception as e:
            print(f"方法1处理第{page_num + 1}页时出错: {e}")
            
            # 方法2: 尝试单独提取图像
            try:
                new_page = image_doc.new_page(width=rect.width, height=rect.height)
                
                # 获取页面上的所有图像
                image_list = page.get_images(full=True)
                
                if image_list:
                    for img_index, img in enumerate(image_list):
                        try:
                            # 提取图像数据
                            xref = img[0]
                            base_image = pdf_doc.extract_image(xref)
                            image_bytes = base_image["image"]
                            
                            # 尝试获取图像位置（这可能不准确）
                            # 简单地将图像放在页面中央
                            img_rect = fitz.Rect(50, 50, rect.width-50, rect.height-50)
                            new_page.insert_image(img_rect, stream=image_bytes)
                            
                        except Exception as img_e:
                            print(f"提取第{page_num + 1}页图像{img_index + 1}时出错: {img_e}")
                
                # 如果没有图像，添加标记
                if not image_list:
                    new_page.insert_text(
                        fitz.Point(50, 50), 
                        f"第{page_num + 1}页 - 无图像内容", 
                        fontsize=12
                    )
                    
            except Exception as e2:
                print(f"方法2处理第{page_num + 1}页时出错: {e2}")
                # 创建空白页面
                new_page = image_doc.new_page(width=rect.width, height=rect.height)
                new_page.insert_text(
                    fitz.Point(50, 50), 
                    f"第{page_num + 1}页 - 图像提取失败", 
                    fontsize=12
                )
    
    # 保存图像PDF
    image_doc.save(output_path)
    image_doc.close()

def main():
    input_pdf = "PDFtest.pdf"
    
    if not os.path.exists(input_pdf):
        print(f"❌ 错误: 找不到文件 {input_pdf}")
        print("请确保PDFtest.pdf文件在当前目录中")
        return
    
    print("🔄 开始改进版PDF分离...")
    print(f"📁 输入文件: {input_pdf}")
    
    success = split_double_layer_pdf_improved(
        input_pdf,
        "PDFtest_text_improved.pdf",
        "PDFtest_images_improved.pdf"
    )
    
    if success:
        print("\n📊 分离统计:")
        # 显示原文件信息
        original_doc = fitz.open(input_pdf)
        print(f"原文件页数: {original_doc.page_count}")
        original_doc.close()
        
        # 显示分离后文件信息
        if os.path.exists("PDFtest_text_improved.pdf"):
            text_doc = fitz.open("PDFtest_text_improved.pdf")
            print(f"改进版文本PDF页数: {text_doc.page_count}")
            text_doc.close()
        
        if os.path.exists("PDFtest_images_improved.pdf"):
            image_doc = fitz.open("PDFtest_images_improved.pdf")
            print(f"改进版图像PDF页数: {image_doc.page_count}")
            image_doc.close()

if __name__ == "__main__":
    main()
