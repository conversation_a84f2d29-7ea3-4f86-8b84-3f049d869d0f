import fitz  # PyMuPDF
from paddleocr import PaddleOCR
import os

# 初始化 PaddleOCR，设置为中文
ocr = PaddleOCR(use_angle_cls=True, lang="ch")  # 'ch' 表示中文，英文用 'en'

# 设置输入和输出路径
input_pdf = "PDFtest.pdf"  # 单个PDF文件路径
output_dir = "output_texts"  # 输出文本文件夹路径
os.makedirs(output_dir, exist_ok=True)

# 处理单个 PDF 的函数
def process_pdf(pdf_path):
    # 获取PDF文件名（不含扩展名）
    pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
    output_path = os.path.join(output_dir, f"{pdf_name}.txt")
    output_text = ""
    pdf = None

    try:
        # 打开 PDF
        pdf = fitz.open(pdf_path)
        print(f"开始处理PDF: {pdf_path}，共 {pdf.page_count} 页")

        for page_num in range(pdf.page_count):
            print(f"正在处理第 {page_num + 1} 页...")
            page = pdf.load_page(page_num)
            # 将页面渲染为图像（300 DPI）
            pix = page.get_pixmap(matrix=fitz.Matrix(300/72, 300/72))
            img_data = pix.tobytes("png")  # 转为 PNG 格式

            # 使用 PaddleOCR 进行文字识别
            result = ocr.ocr(img_data)

            # 检查OCR结果是否为空
            if result and result[0]:
                text = "\n".join([line[1][0] for line in result[0]])  # 提取文字
                output_text += f"第 {page_num + 1} 页:\n{text}\n\n"
            else:
                output_text += f"第 {page_num + 1} 页: 未识别到文字\n\n"

        # 保存提取的文字到文件
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(output_text)
        print(f"处理完成: {pdf_path}")
        print(f"输出文件: {output_path}")

    except Exception as e:
        print(f"处理 {pdf_path} 时出错: {str(e)}")
    finally:
        if pdf:
            pdf.close()

# 检查输入文件是否存在
if os.path.exists(input_pdf):
    if os.path.isfile(input_pdf) and input_pdf.lower().endswith('.pdf'):
        # 处理单个PDF文件
        process_pdf(input_pdf)
    elif os.path.isdir(input_pdf):
        # 如果是目录，批量处理目录中的所有PDF
        pdf_files = [f for f in os.listdir(input_pdf) if f.lower().endswith(".pdf")]
        if pdf_files:
            print(f"找到 {len(pdf_files)} 个PDF文件")
            for pdf_file in pdf_files:
                pdf_path = os.path.join(input_pdf, pdf_file)
                process_pdf(pdf_path)
        else:
            print(f"在目录 {input_pdf} 中未找到PDF文件")
    else:
        print(f"错误: {input_pdf} 不是有效的PDF文件")
else:
    print(f"错误: 文件或目录 {input_pdf} 不存在")